<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Records Visualization</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SheetJS CDN for Excel file reading -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Date adapter for Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Chat Records Visualization Dashboard</h1>
            <div class="file-upload-section">
                <label for="excelFile" class="upload-btn">
                    <span>📁 Upload Excel File</span>
                    <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                </label>
                <span id="fileName" class="file-name"></span>
            </div>
        </header>

        <nav class="chart-navigation">
            <button class="nav-btn active" data-chart="trend">📈 Trend Analysis</button>
            <button class="nav-btn" data-chart="speakers">👥 Speaker Activity</button>
            <button class="nav-btn" data-chart="timeline">⏰ Timeline Patterns</button>
            <button class="nav-btn" data-chart="distribution">📊 Message Distribution</button>
        </nav>

        <main class="chart-container">
            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
                <p>Processing data...</p>
            </div>

            <div id="noDataMessage" class="no-data">
                <h3>📋 No Data Available</h3>
                <p>Please upload an Excel file to begin visualization</p>
            </div>

            <!-- Trend Chart (Dual-axis) -->
            <div id="trendChart" class="chart-section active">
                <div class="chart-header">
                    <h2>📈 Message & Speaker Trend Analysis</h2>
                    <p>Dual-axis chart showing message count and unique speaker count over time</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="trendCanvas"></canvas>
                </div>
            </div>

            <!-- Speaker Activity Chart -->
            <div id="speakersChart" class="chart-section">
                <div class="chart-header">
                    <h2>👥 Speaker Activity Distribution</h2>
                    <p>Top speakers by message count and activity patterns</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="speakersCanvas"></canvas>
                </div>
            </div>

            <!-- Timeline Patterns Chart -->
            <div id="timelineChart" class="chart-section">
                <div class="chart-header">
                    <h2>⏰ Timeline Activity Patterns</h2>
                    <p>Message activity by hour of day and day of week</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="timelineCanvas"></canvas>
                </div>
            </div>

            <!-- Message Distribution Chart -->
            <div id="distributionChart" class="chart-section">
                <div class="chart-header">
                    <h2>📊 Message Length Distribution</h2>
                    <p>Analysis of message lengths and content patterns</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="distributionCanvas"></canvas>
                </div>
            </div>
        </main>

        <footer>
            <div class="stats-summary" id="statsSummary">
                <div class="stat-item">
                    <span class="stat-label">Total Messages:</span>
                    <span class="stat-value" id="totalMessages">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Unique Speakers:</span>
                    <span class="stat-value" id="uniqueSpeakers">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Date Range:</span>
                    <span class="stat-value" id="dateRange">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Avg Messages/Day:</span>
                    <span class="stat-value" id="avgMessagesPerDay">-</span>
                </div>
            </div>
        </footer>
    </div>

    <script src="dataProcessor.js"></script>
    <script src="chartManager.js"></script>
    <script src="app.js"></script>
</body>
</html>
