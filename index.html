<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Records Visualization</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SheetJS CDN for Excel file reading -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Date adapter for Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <!-- Default data file (if exists) -->
    <script src="default_data.js" onerror="console.log('No default data file found')"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Chat Records Visualization Dashboard</h1>
            <div class="file-upload-section">
                <label for="excelFile" class="upload-btn">
                    <span>📁 Upload Excel File</span>
                    <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                </label>
                <span id="fileName" class="file-name"></span>
            </div>
        </header>

        <nav class="chart-navigation">
            <button class="nav-btn active" data-chart="trend">📈 Trend Analysis</button>
            <button class="nav-btn" data-chart="speakers">👥 Speaker Activity</button>
            <button class="nav-btn" data-chart="timeline">⏰ Timeline Patterns</button>
            <button class="nav-btn" data-chart="distribution">📊 Message Distribution</button>
        </nav>

        <main class="chart-container">
            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
                <p>Processing data...</p>
            </div>

            <div id="noDataMessage" class="no-data">
                <h3>📋 No Data Available</h3>
                <p><strong>方法1（推荐）：</strong>使用 <a href="data_converter.html" target="_blank" style="color: #4CAF50; font-weight: bold;">数据转换工具</a> 将您的Excel文件转换为默认数据，实现自动加载</p>
                <p><strong>方法2：</strong>手动上传Excel文件（需包含列：发言日期, 发言人数, 发言总数, 活跃用户top1-5）</p>
                <p><strong>方法3：</strong>使用 <a href="generate_excel.html" target="_blank">Excel生成器</a> 创建示例文件进行测试</p>
            </div>

            <!-- Trend Chart (Dual-axis) -->
            <div id="trendChart" class="chart-section active">
                <div class="chart-header">
                    <h2>📈 Daily Activity Trend Analysis</h2>
                    <p>Dual-axis chart showing daily message count and speaker count over time</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="trendCanvas"></canvas>
                </div>
            </div>

            <!-- Speaker Activity Chart -->
            <div id="speakersChart" class="chart-section">
                <div class="chart-header">
                    <h2>👥 Top Active Users</h2>
                    <p>Most active users by total message count with activity statistics</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="speakersCanvas"></canvas>
                </div>
            </div>

            <!-- Timeline Patterns Chart -->
            <div id="timelineChart" class="chart-section">
                <div class="chart-header">
                    <h2>⏰ Weekly Activity Patterns</h2>
                    <p>Message activity distribution by day of the week</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="timelineCanvas"></canvas>
                </div>
            </div>

            <!-- Message Distribution Chart -->
            <div id="distributionChart" class="chart-section">
                <div class="chart-header">
                    <h2>📊 Daily Message Count Distribution</h2>
                    <p>Distribution of daily message counts across different ranges</p>
                </div>
                <div class="chart-wrapper">
                    <canvas id="distributionCanvas"></canvas>
                </div>
            </div>
        </main>

        <footer>
            <div class="stats-summary" id="statsSummary">
                <div class="stat-item">
                    <span class="stat-label">Total Messages:</span>
                    <span class="stat-value" id="totalMessages">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Unique Speakers:</span>
                    <span class="stat-value" id="uniqueSpeakers">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Date Range:</span>
                    <span class="stat-value" id="dateRange">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Avg Messages/Day:</span>
                    <span class="stat-value" id="avgMessagesPerDay">-</span>
                </div>
            </div>
        </footer>
    </div>

    <script>
        console.log('Page loaded');

        // Global variables
        let dataProcessor = null;
        let chartManager = null;
        let isDataLoaded = false;

        // Chart switching function
        function switchChart(chartType) {
            console.log('Switching to chart:', chartType);

            // Update active button
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            const activeBtn = document.querySelector(`[data-chart="${chartType}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            // Hide all chart sections
            document.querySelectorAll('.chart-section').forEach(section => {
                section.classList.remove('active');
            });

            // Show selected chart section
            const targetSection = document.getElementById(`${chartType}Chart`);
            if (targetSection) {
                targetSection.classList.add('active');
                console.log('Successfully switched to:', chartType);

                // Resize chart if it exists
                if (chartManager && chartManager.charts && chartManager.charts[chartType]) {
                    setTimeout(() => {
                        chartManager.charts[chartType].resize();
                    }, 100);
                }
            } else {
                console.error('Chart section not found:', `${chartType}Chart`);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');

            // Initialize classes
            try {
                dataProcessor = new DataProcessor();
                chartManager = new ChartManager();
                console.log('Classes initialized successfully');
            } catch (error) {
                console.error('Error initializing classes:', error);
            }

            // Check for default data and load it
            checkAndLoadDefaultData();

            // Add event listeners for navigation buttons
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    const chartType = e.target.dataset.chart;
                    switchChart(chartType);
                });
            });

            // Add file upload listener
            document.getElementById('excelFile').addEventListener('change', async function(e) {
                const file = e.target.files[0];
                if (!file) return;

                console.log('File selected:', file.name);
                document.getElementById('fileName').textContent = file.name;

                // Show loading
                document.getElementById('loadingIndicator').classList.add('show');
                document.getElementById('noDataMessage').classList.remove('show');

                try {
                    // Process file
                    await dataProcessor.readExcelFile(file);
                    chartManager.initialize(dataProcessor);
                    chartManager.createAllCharts();

                    // Update stats
                    const stats = dataProcessor.getStats();
                    document.getElementById('totalMessages').textContent = stats.totalMessages.toLocaleString();
                    document.getElementById('uniqueSpeakers').textContent = stats.uniqueSpeakers.toLocaleString();
                    document.getElementById('avgMessagesPerDay').textContent = stats.avgMessagesPerDay.toLocaleString();

                    if (stats.dateRange.start && stats.dateRange.end) {
                        const startDate = stats.dateRange.start.toLocaleDateString();
                        const endDate = stats.dateRange.end.toLocaleDateString();
                        document.getElementById('dateRange').textContent = `${startDate} - ${endDate}`;
                    }

                    isDataLoaded = true;
                    document.getElementById('loadingIndicator').classList.remove('show');

                    // Show success message
                    showNotification('数据加载成功！图表已生成。', 'success');

                } catch (error) {
                    console.error('Error processing file:', error);
                    document.getElementById('loadingIndicator').classList.remove('show');
                    document.getElementById('noDataMessage').classList.add('show');
                    showNotification('文件处理错误: ' + error.message, 'error');
                }
            });

            console.log('Initialization complete');
        });

        // Check and load default data
        async function checkAndLoadDefaultData() {
            console.log('Checking for default data...');

            if (window.HAS_DEFAULT_DATA && window.DEFAULT_EXCEL_DATA) {
                console.log('Default data found, loading...');

                try {
                    // Show loading
                    document.getElementById('loadingIndicator').classList.add('show');
                    document.getElementById('noDataMessage').classList.remove('show');

                    // Process the default data
                    await processDefaultData(window.DEFAULT_EXCEL_DATA);

                    showNotification('✅ 默认数据加载成功！', 'success');

                } catch (error) {
                    console.error('Error loading default data:', error);
                    document.getElementById('loadingIndicator').classList.remove('show');
                    document.getElementById('noDataMessage').classList.add('show');
                    showNotification('❌ 默认数据加载失败: ' + error.message, 'error');
                }
            } else {
                console.log('No default data found');
                showNotification('💡 提示：使用数据转换工具生成 default_data.js 文件可实现自动加载', 'info');
            }
        }

        // Process default data
        async function processDefaultData(rawData) {
            console.log('Processing default data...', rawData.length, 'rows');

            // Set the raw data in dataProcessor
            dataProcessor.rawData = rawData;
            dataProcessor.processedData = dataProcessor.processRawData(rawData);
            dataProcessor.calculateStats();

            // Initialize chart manager with processed data
            chartManager.initialize(dataProcessor);

            // Create all charts
            chartManager.createAllCharts();

            // Update stats
            const stats = dataProcessor.getStats();
            document.getElementById('totalMessages').textContent = stats.totalMessages.toLocaleString();
            document.getElementById('uniqueSpeakers').textContent = stats.uniqueSpeakers.toLocaleString();
            document.getElementById('avgMessagesPerDay').textContent = stats.avgMessagesPerDay.toLocaleString();

            if (stats.dateRange.start && stats.dateRange.end) {
                const startDate = stats.dateRange.start.toLocaleDateString();
                const endDate = stats.dateRange.end.toLocaleDateString();
                document.getElementById('dateRange').textContent = `${startDate} - ${endDate}`;
            }

            isDataLoaded = true;
            document.getElementById('loadingIndicator').classList.remove('show');
            document.getElementById('fileName').textContent = 'default_data.js (默认数据)';

            console.log('Default data processing completed');
        }

        // Simple notification function
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
                color: white;
                padding: 15px 20px;
                border-radius: 5px;
                z-index: 1000;
                max-width: 400px;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>

    <script src="dataProcessor.js"></script>
    <script src="chartManager.js"></script>
</body>
</html>
