<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .active { background: blue; color: white; }
        .chart-section { display: none; padding: 20px; border: 1px solid #ccc; margin: 10px 0; }
        .chart-section.active { display: block; }
    </style>
</head>
<body>
    <h1>Debug Test Page</h1>
    
    <div>
        <button class="nav-btn active" data-chart="trend">📈 Trend Analysis</button>
        <button class="nav-btn" data-chart="speakers">👥 Speaker Activity</button>
        <button class="nav-btn" data-chart="timeline">⏰ Timeline Patterns</button>
        <button class="nav-btn" data-chart="distribution">📊 Message Distribution</button>
    </div>

    <div id="trendChart" class="chart-section active">
        <h2>Trend Chart</h2>
        <p>This is the trend chart section</p>
    </div>

    <div id="speakersChart" class="chart-section">
        <h2>Speakers Chart</h2>
        <p>This is the speakers chart section</p>
    </div>

    <div id="timelineChart" class="chart-section">
        <h2>Timeline Chart</h2>
        <p>This is the timeline chart section</p>
    </div>

    <div id="distributionChart" class="chart-section">
        <h2>Distribution Chart</h2>
        <p>This is the distribution chart section</p>
    </div>

    <script>
        // Simple test of chart switching
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                console.log('Button clicked:', e.target);
                const chartType = e.target.dataset.chart;
                console.log('Chart type:', chartType);
                
                // Update active button
                document.querySelectorAll('.nav-btn').forEach(b => {
                    b.classList.remove('active');
                });
                e.target.classList.add('active');
                
                // Hide all sections
                document.querySelectorAll('.chart-section').forEach(section => {
                    section.classList.remove('active');
                });
                
                // Show target section
                const targetSection = document.getElementById(`${chartType}Chart`);
                console.log('Target section:', targetSection);
                if (targetSection) {
                    targetSection.classList.add('active');
                    console.log('Switched to:', chartType);
                } else {
                    console.error('Section not found:', `${chartType}Chart`);
                }
            });
        });
    </script>
</body>
</html>
