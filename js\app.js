/**
 * Main Application Controller for Chat Records Visualization
 * Handles user interactions, file uploads, and chart management
 */

class ChatVisualizationApp {
    constructor() {
        console.log('ChatVisualizationApp constructor called');
        this.dataProcessor = new DataProcessor();
        this.chartManager = new ChartManager();
        this.isDataLoaded = false;

        console.log('Initializing event listeners...');
        this.initializeEventListeners();
        this.showNoDataMessage();

        // Try to load default file on startup
        console.log('Trying to load default file...');
        this.tryLoadDefaultFile();
        console.log('Constructor completed');
    }

    /**
     * Initialize all event listeners
     */
    initializeEventListeners() {
        console.log('Setting up event listeners...');

        // File upload handler
        const fileInput = document.getElementById('excelFile');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
            console.log('File input listener added');
        } else {
            console.error('File input element not found!');
        }

        // Chart navigation handlers
        const navButtons = document.querySelectorAll('.nav-btn');
        console.log('Found nav buttons:', navButtons.length);
        navButtons.forEach((btn, index) => {
            console.log(`Adding listener to button ${index}:`, btn.dataset.chart);
            btn.addEventListener('click', (e) => this.handleChartSwitch(e));
        });

        // Window resize handler for chart responsiveness
        window.addEventListener('resize', () => this.handleWindowResize());
        console.log('Event listeners setup completed');
    }

    /**
     * Try to load the default Excel file on startup
     */
    async tryLoadDefaultFile() {
        const defaultFileName = '发言记录.xlsx';

        // Due to CORS restrictions when opening HTML files directly,
        // we'll show a helpful message instead of trying to fetch
        console.log(`Looking for default file: ${defaultFileName}`);

        // Check if we're running from file:// protocol
        if (window.location.protocol === 'file:') {
            // Show a helpful message about the default file
            this.showDefaultFileInfo();
        } else {
            // If running from a server, try to fetch the file
            try {
                const response = await fetch(defaultFileName);

                if (response.ok) {
                    const arrayBuffer = await response.arrayBuffer();
                    const blob = new Blob([arrayBuffer], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    });

                    const file = new File([blob], defaultFileName, {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    });

                    await this.processFile(file);
                    console.log(`Successfully loaded default file: ${defaultFileName}`);
                } else {
                    console.log(`Default file ${defaultFileName} not found`);
                }
            } catch (error) {
                console.log(`Could not load default file: ${error.message}`);
            }
        }
    }

    /**
     * Show information about the default file
     */
    showDefaultFileInfo() {
        // Check if the default file exists by trying to create a file input
        // and programmatically trigger it to look for the specific file
        setTimeout(() => {
            this.showInfo('💡 提示：如果您有名为 "发言记录.xlsx" 的文件，请点击上传按钮选择该文件。');
        }, 1000);
    }

    /**
     * Handle Excel file upload
     * @param {Event} event - File input change event
     */
    async handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        await this.processFile(file);
    }

    /**
     * Process an Excel file (from upload or default loading)
     * @param {File} file - Excel file to process
     */
    async processFile(file) {
        // Validate file type
        const validTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!validTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
            this.showError('Please select a valid Excel file (.xlsx or .xls)');
            return;
        }

        // Show loading indicator
        this.showLoading();
        this.updateFileName(file.name);

        try {
            // Process the Excel file
            await this.dataProcessor.readExcelFile(file);

            // Initialize chart manager with processed data
            this.chartManager.initialize(this.dataProcessor);

            // Create all charts
            this.chartManager.createAllCharts();

            // Update UI
            this.updateStatsSummary();
            this.hideLoading();
            this.hideNoDataMessage();
            this.isDataLoaded = true;

            // Show success message
            this.showSuccess('Data loaded successfully! Charts are ready for viewing.');

        } catch (error) {
            console.error('Error processing file:', error);
            this.showError(`Failed to process file: ${error.message}`);
            this.hideLoading();
            this.showNoDataMessage();
        }
    }

    /**
     * Handle chart navigation button clicks
     * @param {Event} event - Button click event
     */
    handleChartSwitch(event) {
        console.log('Chart switch clicked', event.target);

        const button = event.target;
        const chartType = button.dataset.chart;

        console.log('Chart type:', chartType, 'Data loaded:', this.isDataLoaded);

        // Always allow chart switching for UI testing, but show message if no data
        if (!this.isDataLoaded) {
            this.showError('请先上传Excel文件才能查看图表数据');
            // Don't return, allow the UI to switch anyway
        }

        // Update active button
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');

        // Switch chart
        console.log('Switching to chart:', chartType);
        if (this.chartManager) {
            this.chartManager.switchChart(chartType);
        } else {
            // Fallback: manual chart switching
            this.manualSwitchChart(chartType);
        }
    }

    /**
     * Manual chart switching fallback
     * @param {string} chartType - Chart type to switch to
     */
    manualSwitchChart(chartType) {
        console.log('Manual chart switch to:', chartType);

        // Hide all chart sections
        document.querySelectorAll('.chart-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show selected chart section
        const targetSection = document.getElementById(`${chartType}Chart`);
        if (targetSection) {
            targetSection.classList.add('active');
            console.log('Manually switched to chart:', chartType);
        } else {
            console.error('Chart section not found:', `${chartType}Chart`);
        }
    }

    /**
     * Handle window resize for chart responsiveness
     */
    handleWindowResize() {
        if (this.isDataLoaded) {
            // Debounce resize events
            clearTimeout(this.resizeTimeout);
            this.resizeTimeout = setTimeout(() => {
                Object.values(this.chartManager.charts).forEach(chart => {
                    if (chart) {
                        chart.resize();
                    }
                });
            }, 250);
        }
    }

    /**
     * Update the filename display
     * @param {string} filename - Name of uploaded file
     */
    updateFileName(filename) {
        const fileNameElement = document.getElementById('fileName');
        fileNameElement.textContent = filename;
        fileNameElement.title = filename;
    }

    /**
     * Update statistics summary
     */
    updateStatsSummary() {
        const stats = this.dataProcessor.getStats();
        
        document.getElementById('totalMessages').textContent = stats.totalMessages.toLocaleString();
        document.getElementById('uniqueSpeakers').textContent = stats.uniqueSpeakers.toLocaleString();
        document.getElementById('avgMessagesPerDay').textContent = stats.avgMessagesPerDay.toLocaleString();
        
        if (stats.dateRange.start && stats.dateRange.end) {
            const startDate = stats.dateRange.start.toLocaleDateString();
            const endDate = stats.dateRange.end.toLocaleDateString();
            document.getElementById('dateRange').textContent = `${startDate} - ${endDate}`;
        }
    }

    /**
     * Show loading indicator
     */
    showLoading() {
        document.getElementById('loadingIndicator').classList.add('show');
        document.getElementById('noDataMessage').classList.remove('show');
        document.querySelectorAll('.chart-section').forEach(section => {
            section.style.display = 'none';
        });
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        document.getElementById('loadingIndicator').classList.remove('show');
        document.querySelectorAll('.chart-section').forEach(section => {
            section.style.display = 'block';
        });
        // Show the currently active chart
        const activeBtn = document.querySelector('.nav-btn.active');
        if (activeBtn) {
            const chartType = activeBtn.dataset.chart;
            this.chartManager.switchChart(chartType);
        }
    }

    /**
     * Show no data message
     */
    showNoDataMessage() {
        document.getElementById('noDataMessage').classList.add('show');
        document.querySelectorAll('.chart-section').forEach(section => {
            section.style.display = 'none';
        });
    }

    /**
     * Hide no data message
     */
    hideNoDataMessage() {
        document.getElementById('noDataMessage').classList.remove('show');
    }

    /**
     * Show success message
     * @param {string} message - Success message to display
     */
    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    /**
     * Show error message
     * @param {string} message - Error message to display
     */
    showError(message) {
        this.showNotification(message, 'error');
    }

    /**
     * Show info message
     * @param {string} message - Info message to display
     */
    showInfo(message) {
        this.showNotification(message, 'info');
    }

    /**
     * Show notification
     * @param {string} message - Message to display
     * @param {string} type - Notification type (success, error, info)
     */
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        `;

        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            max-width: 400px;
            animation: slideIn 0.3s ease-out;
        `;

        // Add animation keyframes
        if (!document.querySelector('#notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
                .notification-close {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            `;
            document.head.appendChild(style);
        }

        // Add close functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        });

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOut 0.3s ease-in';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);

        document.body.appendChild(notification);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing ChatVisualizationApp...');
    window.chatApp = new ChatVisualizationApp();
    console.log('ChatVisualizationApp initialized:', window.chatApp);
});

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatVisualizationApp;
}
