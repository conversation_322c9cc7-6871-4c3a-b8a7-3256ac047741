# 更新日志 (Changelog)

## 版本 2.3 - 默认数据自动加载功能

### ✨ 新功能
- **默认数据自动加载**：页面启动时自动加载 `default_data.js` 文件中的数据
- **数据转换工具**：新增 `data_converter.html`，可将Excel文件转换为JavaScript数据文件
- **示例数据**：包含30天的示例数据，打开页面即可看到效果

### 🔧 技术实现
- **绕过CORS限制**：通过预处理Excel数据为JavaScript文件，解决浏览器安全限制
- **智能数据检测**：自动检测并加载默认数据，无需手动上传
- **无缝集成**：默认数据加载与手动上传使用相同的处理流程

### 📁 新增文件
- `data_converter.html`：Excel到JavaScript数据转换工具
- `default_data.js`：默认示例数据文件

---

## 版本 2.2 - 修复关键问题

### 🐛 问题修复
- **修复图表切换功能**：解决了导航按钮点击无反应的问题
- **简化应用程序架构**：重构了主应用程序，提高稳定性和可靠性
- **改进错误处理**：添加了更好的调试信息和错误提示

### 🔧 技术改进
- **简化事件处理**：直接在HTML中处理事件，避免复杂的类结构问题
- **增强调试功能**：添加了详细的控制台日志，便于问题排查
- **备用机制**：为图表切换添加了备用处理机制

### 📁 新增文件
- `debug.html`：调试页面，用于测试基本功能
- `test.html`：简单的测试页面
- `index_simple.html`：简化版本的主页面

---

## 版本 2.1 - 图表优化和自动加载功能

### ✨ 新功能
- **Excel文件生成器**：新增 `generate_excel.html` 工具，可生成示例Excel文件
- **智能文件提示**：页面会提示用户上传 `发言记录.xlsx` 文件

### 🔧 图表优化
- **趋势图Y轴优化**：双轴图表现在总是从零开始，确保数据对比的准确性
- **更好的用户体验**：改进了用户界面和交互体验

### 📁 新增文件
- `generate_excel.html`：Excel文件生成工具

---

## 版本 2.0 - 支持新的Excel数据格式

### 🔄 重大更改
- **完全重构数据处理逻辑**：现在支持特定的Excel列格式
- **新的列名支持**：
  - 发言日期
  - 发言人数  
  - 发言总数
  - 活跃用户top1-5

### 📊 数据格式更新
- **活跃用户格式**：支持 "用户名 (消息数量)" 格式，如 "吴双 (36)"
- **日期格式**：支持多种日期格式的自动识别
- **数据验证**：增强的数据验证和错误处理

### 🎨 图表更新
1. **趋势分析图**
   - 显示每日消息总数和发言人数的趋势
   - 双轴显示，更清晰的数据对比

2. **活跃用户图**  
   - 显示总消息数最多的前15名用户
   - 工具提示显示活跃天数、平均排名、日均消息数

3. **周活跃模式图**
   - 按星期几显示消息分布
   - 圆环图展示周活跃模式

4. **消息分布图**
   - 显示每日消息数量的分布情况
   - 极坐标图展示不同消息数量范围的天数分布

### 🛠 技术改进
- **智能列识别**：自动识别Excel文件中的列类型
- **用户信息解析**：解析 "用户名 (数量)" 格式的用户数据
- **错误处理**：更好的错误提示和数据验证
- **性能优化**：优化大数据集的处理性能

### 📁 新增文件
- `sample_data.csv`：示例数据文件，展示正确的数据格式
- `CHANGELOG.md`：更新日志文档

### 🔧 修复问题
- 修复了日期解析的兼容性问题
- 改进了图表响应式设计
- 优化了用户界面的交互体验

### 📖 文档更新
- 更新了README.md，详细说明新的数据格式要求
- 添加了示例数据和使用说明
- 更新了图表类型的详细说明

---

## 使用说明

### 数据格式要求
您的Excel文件需要包含以下列：

```
发言日期 | 发言人数 | 发言总数 | 活跃用户top1 | 活跃用户top2 | 活跃用户top3 | 活跃用户top4 | 活跃用户top5
```

### 示例数据行
```
2024-01-15 | 25 | 156 | 吴双 (36) | 李明 (28) | 王芳 (22) | 张三 (18) | 赵六 (15)
```

### 快速开始
1. 打开 `index.html`
2. 点击"上传Excel文件"按钮
3. 选择您的Excel文件（.xlsx 或 .xls）
4. 使用导航按钮切换不同的图表视图

### 支持的文件格式
- Excel 2007+ (.xlsx)
- Excel 97-2003 (.xls)
- CSV文件（可以先用Excel打开再保存为.xlsx）

---

## 技术栈

- **前端框架**：原生JavaScript (ES6+)
- **图表库**：Chart.js v4.x
- **Excel解析**：SheetJS
- **样式**：CSS3 with Flexbox/Grid
- **兼容性**：现代浏览器 (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)

---

## 开发者信息

如需自定义或扩展功能，请参考以下文件结构：

- `index.html` - 主页面结构
- `styles.css` - 样式和响应式设计  
- `dataProcessor.js` - 数据处理和解析逻辑
- `chartManager.js` - 图表创建和管理
- `app.js` - 主应用控制器
- `sample_data.csv` - 示例数据文件
