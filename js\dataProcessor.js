/**
 * Data Processing Module for Chat Records Visualization
 * Handles Excel file reading, parsing, and data transformation
 */

class DataProcessor {
    constructor() {
        this.rawData = null;
        this.processedData = null;
        this.stats = {
            totalMessages: 0,
            uniqueSpeakers: 0,
            dateRange: { start: null, end: null },
            avgMessagesPerDay: 0
        };
    }

    /**
     * Read and parse Excel file
     * @param {File} file - Excel file from input
     * @returns {Promise<Object>} Processed data object
     */
    async readExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    // Get the first worksheet
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    
                    // Convert to JSON
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
                        header: 1,
                        defval: '',
                        raw: false
                    });
                    
                    this.rawData = jsonData;
                    this.processedData = this.processRawData(jsonData);
                    this.calculateStats();
                    
                    resolve(this.processedData);
                } catch (error) {
                    reject(new Error(`Failed to parse Excel file: ${error.message}`));
                }
            };
            
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * Process raw Excel data into structured format
     * @param {Array} rawData - Raw data from Excel
     * @returns {Object} Processed data structure
     */
    processRawData(rawData) {
        if (!rawData || rawData.length < 2) {
            throw new Error('Invalid data format: insufficient rows');
        }

        // Assume first row contains headers
        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // Try to identify column indices based on common patterns
        const columnMap = this.identifyColumns(headers);
        
        const dailyRecords = [];
        const allSpeakers = new Set();
        const dailyStats = new Map();
        const weeklyStats = Array.from({ length: 7 }, () => 0);
        const speakerActivityMap = new Map(); // 用于统计每个用户的总活跃度

        dataRows.forEach((row, index) => {
            try {
                const dailyRecord = this.parseDailyRow(row, columnMap);
                if (dailyRecord) {
                    dailyRecords.push(dailyRecord);
                    
                    // 更新每日统计
                    const dateKey = dailyRecord.date.toDateString();
                    dailyStats.set(dateKey, {
                        messageCount: dailyRecord.messageCount,
                        speakerCount: dailyRecord.speakerCount,
                        topUsers: dailyRecord.topUsers
                    });
                    
                    // 更新周统计
                    const dayOfWeek = dailyRecord.date.getDay();
                    weeklyStats[dayOfWeek] += dailyRecord.messageCount;
                    
                    // 收集所有用户并统计总活跃度
                    dailyRecord.topUsers.forEach(user => {
                        allSpeakers.add(user.name);
                        if (!speakerActivityMap.has(user.name)) {
                            speakerActivityMap.set(user.name, 0);
                        }
                        speakerActivityMap.set(user.name, speakerActivityMap.get(user.name) + user.count);
                    });
                }
            } catch (error) {
                console.warn(`Skipping row ${index + 2}: ${error.message}`);
            }
        });

        return {
            dailyRecords,
            speakers: Array.from(allSpeakers),
            dailyStats,
            weeklyStats,
            speakerActivityMap,
            columnMap,
            headers
        };
    }

    /**
     * Identify column indices based on header names
     * @param {Array} headers - Header row from Excel
     * @returns {Object} Column mapping
     */
    identifyColumns(headers) {
        const columnMap = {
            date: -1,              // 发言日期
            speakerCount: -1,      // 发言人数
            messageCount: -1,      // 发言总数
            topUsers: []           // 活跃用户top1-5
        };

        headers.forEach((header, index) => {
            const headerStr = header.toString().trim();
            
            // 发言日期
            if (headerStr === '发言日期' || headerStr.includes('发言日期')) {
                columnMap.date = index;
            }
            
            // 发言人数
            if (headerStr === '发言人数' || headerStr.includes('发言人数')) {
                columnMap.speakerCount = index;
            }
            
            // 发言总数
            if (headerStr === '发言总数' || headerStr.includes('发言总数')) {
                columnMap.messageCount = index;
            }
            
            // 活跃用户top1-5
            if (headerStr.match(/活跃用户top[1-5]/)) {
                const topNumber = parseInt(headerStr.match(/top(\d)/)[1]);
                columnMap.topUsers.push({
                    index: index,
                    rank: topNumber,
                    header: headerStr
                });
            }
        });

        // Sort top users by rank
        columnMap.topUsers.sort((a, b) => a.rank - b.rank);

        return columnMap;
    }

    /**
     * Parse a single daily record row
     * @param {Array} row - Data row
     * @param {Object} columnMap - Column mapping
     * @returns {Object|null} Parsed daily record data
     */
    parseDailyRow(row, columnMap) {
        let date = null;
        let speakerCount = 0;
        let messageCount = 0;
        let topUsers = [];

        // Extract date
        if (columnMap.date >= 0 && row[columnMap.date]) {
            date = this.parseDateTime(row[columnMap.date]);
        }

        // Extract speaker count
        if (columnMap.speakerCount >= 0 && row[columnMap.speakerCount]) {
            speakerCount = parseInt(row[columnMap.speakerCount]) || 0;
        }

        // Extract message count
        if (columnMap.messageCount >= 0 && row[columnMap.messageCount]) {
            messageCount = parseInt(row[columnMap.messageCount]) || 0;
        }

        // Extract top users
        columnMap.topUsers.forEach(topUserCol => {
            if (row[topUserCol.index]) {
                const userInfo = this.parseTopUserInfo(row[topUserCol.index].toString());
                if (userInfo) {
                    topUsers.push({
                        rank: topUserCol.rank,
                        name: userInfo.name,
                        count: userInfo.count
                    });
                }
            }
        });

        // Validate required fields
        if (!date) {
            return null;
        }

        return {
            date,
            speakerCount,
            messageCount,
            topUsers
        };
    }

    /**
     * Parse top user information from format "用户名 (数量)"
     * @param {string} userStr - User string like "吴双 (36)"
     * @returns {Object|null} Parsed user info
     */
    parseTopUserInfo(userStr) {
        if (!userStr || typeof userStr !== 'string') {
            return null;
        }

        const trimmed = userStr.trim();
        if (!trimmed) {
            return null;
        }

        // Match pattern: "用户名 (数量)" or "用户名(数量)"
        const match = trimmed.match(/^(.+?)\s*\((\d+)\)$/);
        if (match) {
            const name = match[1].trim();
            const count = parseInt(match[2]);
            
            if (name && !isNaN(count)) {
                return { name, count };
            }
        }

        return null;
    }

    /**
     * Parse various date/time formats
     * @param {string} dateTimeStr - Date/time string
     * @returns {Date|null} Parsed date
     */
    parseDateTime(dateTimeStr) {
        if (!dateTimeStr) return null;
        
        const str = dateTimeStr.toString().trim();
        if (!str) return null;

        // Try different parsing approaches
        let date = null;

        // Try direct Date parsing first
        date = new Date(str);
        if (!isNaN(date.getTime())) {
            return date;
        }

        // Try Excel serial date format
        if (/^\d+(\.\d+)?$/.test(str)) {
            const excelDate = parseFloat(str);
            // Excel epoch starts from 1900-01-01, but has a leap year bug
            const excelEpoch = new Date(1900, 0, 1);
            date = new Date(excelEpoch.getTime() + (excelDate - 1) * 24 * 60 * 60 * 1000);
            if (!isNaN(date.getTime())) {
                return date;
            }
        }

        return null;
    }

    /**
     * Calculate summary statistics
     */
    calculateStats() {
        if (!this.processedData || !this.processedData.dailyRecords) {
            return;
        }

        const dailyRecords = this.processedData.dailyRecords;

        // Calculate total messages and unique speakers
        this.stats.totalMessages = dailyRecords.reduce((sum, record) => sum + record.messageCount, 0);
        this.stats.uniqueSpeakers = this.processedData.speakers.length;

        if (dailyRecords.length > 0) {
            const dates = dailyRecords.map(r => r.date).sort((a, b) => a - b);
            this.stats.dateRange.start = dates[0];
            this.stats.dateRange.end = dates[dates.length - 1];

            const daysDiff = Math.ceil((this.stats.dateRange.end - this.stats.dateRange.start) / (1000 * 60 * 60 * 24)) + 1;
            this.stats.avgMessagesPerDay = Math.round(this.stats.totalMessages / daysDiff * 10) / 10;
        }
    }

    /**
     * Get processed data for specific chart type
     * @param {string} chartType - Type of chart data needed
     * @returns {Object} Chart-specific data
     */
    getChartData(chartType) {
        if (!this.processedData) {
            return null;
        }

        switch (chartType) {
            case 'trend':
                return this.getTrendData();
            case 'speakers':
                return this.getSpeakerData();
            case 'timeline':
                return this.getTimelineData();
            case 'distribution':
                return this.getDistributionData();
            default:
                return null;
        }
    }

    /**
     * Get trend data for dual-axis chart
     */
    getTrendData() {
        const sortedData = this.processedData.dailyRecords.sort((a, b) => a.date - b.date);

        return {
            labels: sortedData.map(d => d.date),
            messageCount: sortedData.map(d => d.messageCount),
            speakerCount: sortedData.map(d => d.speakerCount)
        };
    }

    /**
     * Get speaker activity data
     */
    getSpeakerData() {
        // Convert speakerActivityMap to sorted array
        const sortedSpeakers = Array.from(this.processedData.speakerActivityMap.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 15); // Top 15 speakers

        // Calculate additional stats for each speaker
        const speakerDetails = sortedSpeakers.map(([speakerName, totalCount]) => {
            let activeDays = 0;
            let totalRankSum = 0;
            let rankCount = 0;

            // Count active days and calculate average rank for this speaker
            this.processedData.dailyRecords.forEach(record => {
                const userInDay = record.topUsers.find(user => user.name === speakerName);
                if (userInDay) {
                    activeDays++;
                    totalRankSum += userInDay.rank;
                    rankCount++;
                }
            });

            const avgRank = rankCount > 0 ? Math.round(totalRankSum / rankCount * 10) / 10 : 0;
            const avgMessagesPerDay = activeDays > 0 ? Math.round(totalCount / activeDays * 10) / 10 : 0;

            return {
                name: speakerName,
                totalCount,
                activeDays,
                avgRank,
                avgMessagesPerDay
            };
        });

        return {
            labels: speakerDetails.map(s => s.name),
            messageCount: speakerDetails.map(s => s.totalCount),
            activeDays: speakerDetails.map(s => s.activeDays),
            avgRank: speakerDetails.map(s => s.avgRank),
            avgMessagesPerDay: speakerDetails.map(s => s.avgMessagesPerDay)
        };
    }

    /**
     * Get timeline pattern data
     */
    getTimelineData() {
        return {
            hourly: {
                labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                data: this.processedData.hourlyStats
            },
            weekly: {
                labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
                data: this.processedData.weeklyStats
            }
        };
    }

    /**
     * Get message distribution data (based on daily message counts)
     */
    getDistributionData() {
        const dailyCounts = this.processedData.dailyRecords.map(r => r.messageCount);

        if (dailyCounts.length === 0) {
            return {
                labels: [],
                data: [],
                avgCount: 0,
                maxCount: 0,
                minCount: 0
            };
        }

        // Define ranges for daily message counts
        const countRanges = [
            { label: '1-50', min: 1, max: 50 },
            { label: '51-100', min: 51, max: 100 },
            { label: '101-200', min: 101, max: 200 },
            { label: '201-500', min: 201, max: 500 },
            { label: '501-1000', min: 501, max: 1000 },
            { label: '1000+', min: 1001, max: Infinity }
        ];

        const distribution = countRanges.map(range => ({
            label: range.label,
            count: dailyCounts.filter(count => count >= range.min && count <= range.max).length
        }));

        return {
            labels: distribution.map(d => d.label),
            data: distribution.map(d => d.count),
            avgCount: Math.round(dailyCounts.reduce((sum, count) => sum + count, 0) / dailyCounts.length),
            maxCount: Math.max(...dailyCounts),
            minCount: Math.min(...dailyCounts)
        };
    }

    /**
     * Get summary statistics
     */
    getStats() {
        return this.stats;
    }
}