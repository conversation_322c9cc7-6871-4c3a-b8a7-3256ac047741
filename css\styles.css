/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

h1 {
    color: #2c3e50;
    font-size: 2.2rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-upload-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.upload-btn {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.file-name {
    color: #2c3e50;
    font-weight: 500;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Navigation Styles */
.chart-navigation {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
}

.nav-btn {
    background: transparent;
    border: 2px solid #e0e0e0;
    color: #666;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.nav-btn:hover {
    border-color: #667eea;
    color: #667eea;
    transform: translateY(-1px);
}

.nav-btn.active {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-color: transparent;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* Main Content Styles */
main {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
}

.chart-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.chart-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.chart-header {
    text-align: center;
    margin-bottom: 30px;
}

.chart-header h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 8px;
}

.chart-header p {
    color: #666;
    font-size: 1rem;
}

.chart-wrapper {
    position: relative;
    height: 500px;
    margin: 0 auto;
}

.chart-wrapper canvas {
    max-height: 100%;
    width: 100% !important;
    height: auto !important;
}

/* Loading and No Data Styles */
.loading, .no-data {
    display: none;
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.loading.show, .no-data.show {
    display: block;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Footer Stats */
footer {
    margin-top: 20px;
}

.stats-summary {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.stat-label {
    display: block;
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header {
        flex-direction: column;
        text-align: center;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    .chart-navigation {
        padding: 10px;
    }
    
    .nav-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
    
    main {
        padding: 20px;
    }
    
    .chart-wrapper {
        height: 400px;
    }
    
    .stats-summary {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 480px) {
    .chart-wrapper {
        height: 300px;
    }
    
    .stats-summary {
        grid-template-columns: 1fr;
    }
}
