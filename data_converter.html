<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据转换工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            background: #fafafa;
        }
        .upload-area.dragover {
            border-color: #4CAF50;
            background: #f0f8f0;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #4CAF50;
            color: #2e7d32;
        }
        .error {
            background: #ffebee;
            border-left-color: #f44336;
            color: #c62828;
        }
        #output {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Excel数据转换工具</h1>
        
        <div class="info">
            <p><strong>功能说明：</strong></p>
            <p>1. 上传您的 "发言记录.xlsx" 文件</p>
            <p>2. 工具会将Excel数据转换为JavaScript格式</p>
            <p>3. 生成的 "default_data.js" 文件可以让页面默认加载数据</p>
            <p>4. 将生成的文件保存到项目目录中，页面就能自动显示图表</p>
        </div>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 拖拽Excel文件到这里，或者</p>
            <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
            <button onclick="document.getElementById('fileInput').click()">选择Excel文件</button>
        </div>
        
        <div id="status" class="info hidden"></div>
        
        <div id="outputSection" class="hidden">
            <h3>生成的JavaScript代码：</h3>
            <div id="output"></div>
            <button id="downloadBtn" onclick="downloadJS()">下载 default_data.js 文件</button>
            <button onclick="copyToClipboard()">复制代码</button>
        </div>
    </div>

    <script>
        let processedData = null;
        let jsContent = '';

        // 文件上传处理
        document.getElementById('fileInput').addEventListener('change', handleFile);
        
        // 拖拽上传
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile({ target: { files: files } });
            }
        });

        async function handleFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            showStatus('正在处理文件...', 'info');

            try {
                // 读取Excel文件
                const arrayBuffer = await file.arrayBuffer();
                const workbook = XLSX.read(arrayBuffer, { type: 'array' });
                
                // 获取第一个工作表
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
                
                // 转换为JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
                    header: 1,
                    defval: '',
                    raw: false
                });

                if (jsonData.length < 2) {
                    throw new Error('Excel文件数据不足，至少需要标题行和一行数据');
                }

                // 验证列格式
                const headers = jsonData[0];
                const requiredColumns = ['发言日期', '发言人数', '发言总数', '活跃用户top1', '活跃用户top2', '活跃用户top3', '活跃用户top4', '活跃用户top5'];
                
                const missingColumns = requiredColumns.filter(col => !headers.includes(col));
                if (missingColumns.length > 0) {
                    throw new Error(`缺少必需的列: ${missingColumns.join(', ')}`);
                }

                // 生成JavaScript代码
                generateJavaScriptCode(jsonData);
                
                showStatus(`✅ 成功处理 ${jsonData.length - 1} 行数据`, 'success');
                document.getElementById('outputSection').classList.remove('hidden');

            } catch (error) {
                console.error('处理文件时出错:', error);
                showStatus(`❌ 处理失败: ${error.message}`, 'error');
            }
        }

        function generateJavaScriptCode(jsonData) {
            // 将数据转换为JavaScript格式
            const dataString = JSON.stringify(jsonData, null, 2);
            
            jsContent = `// 默认数据文件 - 由Excel转换工具自动生成
// 生成时间: ${new Date().toLocaleString()}

// 原始Excel数据
window.DEFAULT_EXCEL_DATA = ${dataString};

// 数据加载标志
window.HAS_DEFAULT_DATA = true;

console.log('默认数据已加载，共', window.DEFAULT_EXCEL_DATA.length - 1, '行数据');
`;

            // 显示生成的代码
            document.getElementById('output').textContent = jsContent;
        }

        function downloadJS() {
            if (!jsContent) {
                alert('请先处理Excel文件');
                return;
            }

            const blob = new Blob([jsContent], { type: 'application/javascript' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'default_data.js';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showStatus('✅ default_data.js 文件已下载！请将其保存到项目目录中。', 'success');
        }

        function copyToClipboard() {
            if (!jsContent) {
                alert('请先处理Excel文件');
                return;
            }

            navigator.clipboard.writeText(jsContent).then(() => {
                showStatus('✅ 代码已复制到剪贴板', 'success');
            }).catch(() => {
                // 备用方法
                const textArea = document.createElement('textarea');
                textArea.value = jsContent;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showStatus('✅ 代码已复制到剪贴板', 'success');
            });
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `info ${type}`;
            statusDiv.classList.remove('hidden');
        }
    </script>
</body>
</html>
