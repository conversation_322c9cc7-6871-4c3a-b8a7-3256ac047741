/**
 * Data Processing Module for Chat Records Visualization
 * Handles Excel file reading, parsing, and data transformation
 */

class DataProcessor {
    constructor() {
        this.rawData = null;
        this.processedData = null;
        this.stats = {
            totalMessages: 0,
            uniqueSpeakers: 0,
            dateRange: { start: null, end: null },
            avgMessagesPerDay: 0
        };
    }

    /**
     * Read and parse Excel file
     * @param {File} file - Excel file from input
     * @returns {Promise<Object>} Processed data object
     */
    async readExcelFile(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    
                    // Get the first worksheet
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    
                    // Convert to JSON
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
                        header: 1,
                        defval: '',
                        raw: false
                    });
                    
                    this.rawData = jsonData;
                    this.processedData = this.processRawData(jsonData);
                    this.calculateStats();
                    
                    resolve(this.processedData);
                } catch (error) {
                    reject(new Error(`Failed to parse Excel file: ${error.message}`));
                }
            };
            
            reader.onerror = () => reject(new Error('Failed to read file'));
            reader.readAsArrayBuffer(file);
        });
    }

    /**
     * Process raw Excel data into structured format
     * @param {Array} rawData - Raw data from Excel
     * @returns {Object} Processed data structure
     */
    processRawData(rawData) {
        if (!rawData || rawData.length < 2) {
            throw new Error('Invalid data format: insufficient rows');
        }

        // Assume first row contains headers
        const headers = rawData[0];
        const dataRows = rawData.slice(1);

        // Try to identify column indices based on common patterns
        const columnMap = this.identifyColumns(headers);
        
        const messages = [];
        const speakers = new Set();
        const dailyStats = new Map();
        const hourlyStats = new Array(24).fill(0);
        const weeklyStats = new Array(7).fill(0);

        dataRows.forEach((row, index) => {
            try {
                const messageData = this.parseMessageRow(row, columnMap);
                if (messageData) {
                    messages.push(messageData);
                    speakers.add(messageData.speaker);
                    
                    // Update daily statistics
                    const dateKey = messageData.date.toDateString();
                    dailyStats.set(dateKey, (dailyStats.get(dateKey) || 0) + 1);
                    
                    // Update hourly statistics
                    const hour = messageData.date.getHours();
                    hourlyStats[hour]++;
                    
                    // Update weekly statistics
                    const dayOfWeek = messageData.date.getDay();
                    weeklyStats[dayOfWeek]++;
                }
            } catch (error) {
                console.warn(`Skipping row ${index + 2}: ${error.message}`);
            }
        });

        return {
            messages,
            speakers: Array.from(speakers),
            dailyStats,
            hourlyStats,
            weeklyStats,
            columnMap,
            headers
        };
    }

    /**
     * Identify column indices based on header names
     * @param {Array} headers - Header row from Excel
     * @returns {Object} Column mapping
     */
    identifyColumns(headers) {
        const columnMap = {
            timestamp: -1,
            speaker: -1,
            message: -1,
            date: -1,
            time: -1
        };

        headers.forEach((header, index) => {
            const headerLower = header.toString().toLowerCase();
            
            // Timestamp patterns
            if (headerLower.includes('时间') || headerLower.includes('timestamp') || 
                headerLower.includes('datetime') || headerLower.includes('发言时间')) {
                columnMap.timestamp = index;
            }
            
            // Speaker patterns
            if (headerLower.includes('发言人') || headerLower.includes('speaker') || 
                headerLower.includes('用户') || headerLower.includes('user') ||
                headerLower.includes('姓名') || headerLower.includes('name')) {
                columnMap.speaker = index;
            }
            
            // Message patterns
            if (headerLower.includes('内容') || headerLower.includes('message') || 
                headerLower.includes('发言内容') || headerLower.includes('text') ||
                headerLower.includes('消息') || headerLower.includes('content')) {
                columnMap.message = index;
            }
            
            // Date patterns
            if (headerLower.includes('日期') || headerLower.includes('date')) {
                columnMap.date = index;
            }
            
            // Time patterns
            if (headerLower.includes('时刻') || headerLower.includes('time') && !headerLower.includes('timestamp')) {
                columnMap.time = index;
            }
        });

        return columnMap;
    }

    /**
     * Parse a single message row
     * @param {Array} row - Data row
     * @param {Object} columnMap - Column mapping
     * @returns {Object|null} Parsed message data
     */
    parseMessageRow(row, columnMap) {
        let date = null;
        let speaker = '';
        let message = '';

        // Extract speaker
        if (columnMap.speaker >= 0 && row[columnMap.speaker]) {
            speaker = row[columnMap.speaker].toString().trim();
        }

        // Extract message
        if (columnMap.message >= 0 && row[columnMap.message]) {
            message = row[columnMap.message].toString().trim();
        }

        // Extract date/time
        if (columnMap.timestamp >= 0 && row[columnMap.timestamp]) {
            date = this.parseDateTime(row[columnMap.timestamp]);
        } else if (columnMap.date >= 0 && columnMap.time >= 0) {
            const dateStr = row[columnMap.date]?.toString() || '';
            const timeStr = row[columnMap.time]?.toString() || '';
            date = this.parseDateTime(`${dateStr} ${timeStr}`);
        } else if (columnMap.date >= 0) {
            date = this.parseDateTime(row[columnMap.date]);
        }

        // Validate required fields
        if (!speaker || !date) {
            return null;
        }

        return {
            date,
            speaker,
            message,
            messageLength: message.length,
            hour: date.getHours(),
            dayOfWeek: date.getDay()
        };
    }

    /**
     * Parse various date/time formats
     * @param {string} dateTimeStr - Date/time string
     * @returns {Date|null} Parsed date
     */
    parseDateTime(dateTimeStr) {
        if (!dateTimeStr) return null;
        
        const str = dateTimeStr.toString().trim();
        if (!str) return null;

        // Try different parsing approaches
        let date = null;

        // Try direct Date parsing first
        date = new Date(str);
        if (!isNaN(date.getTime())) {
            return date;
        }

        // Try Excel serial date format
        if (/^\d+(\.\d+)?$/.test(str)) {
            const excelDate = parseFloat(str);
            // Excel epoch starts from 1900-01-01, but has a leap year bug
            const excelEpoch = new Date(1900, 0, 1);
            date = new Date(excelEpoch.getTime() + (excelDate - 1) * 24 * 60 * 60 * 1000);
            if (!isNaN(date.getTime())) {
                return date;
            }
        }

        // Try common formats
        const formats = [
            /(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})/,
            /(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})\s+(\d{1,2}):(\d{1,2})/,
            /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})/,
            /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})\s+(\d{1,2}):(\d{1,2})/
        ];

        for (const format of formats) {
            const match = str.match(format);
            if (match) {
                try {
                    if (match[1].length === 4) {
                        // Year first format
                        date = new Date(
                            parseInt(match[1]), 
                            parseInt(match[2]) - 1, 
                            parseInt(match[3]),
                            parseInt(match[4] || 0),
                            parseInt(match[5] || 0),
                            parseInt(match[6] || 0)
                        );
                    } else {
                        // Month/day first format
                        date = new Date(
                            parseInt(match[3]), 
                            parseInt(match[1]) - 1, 
                            parseInt(match[2]),
                            parseInt(match[4] || 0),
                            parseInt(match[5] || 0),
                            parseInt(match[6] || 0)
                        );
                    }
                    if (!isNaN(date.getTime())) {
                        return date;
                    }
                } catch (e) {
                    continue;
                }
            }
        }

        return null;
    }

    /**
     * Calculate summary statistics
     */
    calculateStats() {
        if (!this.processedData || !this.processedData.messages) {
            return;
        }

        const messages = this.processedData.messages;
        this.stats.totalMessages = messages.length;
        this.stats.uniqueSpeakers = this.processedData.speakers.length;

        if (messages.length > 0) {
            const dates = messages.map(m => m.date).sort((a, b) => a - b);
            this.stats.dateRange.start = dates[0];
            this.stats.dateRange.end = dates[dates.length - 1];

            const daysDiff = Math.ceil((this.stats.dateRange.end - this.stats.dateRange.start) / (1000 * 60 * 60 * 24)) + 1;
            this.stats.avgMessagesPerDay = Math.round(this.stats.totalMessages / daysDiff * 10) / 10;
        }
    }

    /**
     * Get processed data for specific chart type
     * @param {string} chartType - Type of chart data needed
     * @returns {Object} Chart-specific data
     */
    getChartData(chartType) {
        if (!this.processedData) {
            return null;
        }

        switch (chartType) {
            case 'trend':
                return this.getTrendData();
            case 'speakers':
                return this.getSpeakerData();
            case 'timeline':
                return this.getTimelineData();
            case 'distribution':
                return this.getDistributionData();
            default:
                return null;
        }
    }

    /**
     * Get trend data for dual-axis chart
     */
    getTrendData() {
        const dailyData = new Map();
        
        this.processedData.messages.forEach(msg => {
            const dateKey = msg.date.toDateString();
            if (!dailyData.has(dateKey)) {
                dailyData.set(dateKey, {
                    date: new Date(msg.date.getFullYear(), msg.date.getMonth(), msg.date.getDate()),
                    messageCount: 0,
                    speakers: new Set()
                });
            }
            
            const dayData = dailyData.get(dateKey);
            dayData.messageCount++;
            dayData.speakers.add(msg.speaker);
        });

        const sortedData = Array.from(dailyData.values()).sort((a, b) => a.date - b.date);
        
        return {
            labels: sortedData.map(d => d.date),
            messageCount: sortedData.map(d => d.messageCount),
            speakerCount: sortedData.map(d => d.speakers.size)
        };
    }

    /**
     * Get speaker activity data
     */
    getSpeakerData() {
        const speakerStats = new Map();
        
        this.processedData.messages.forEach(msg => {
            if (!speakerStats.has(msg.speaker)) {
                speakerStats.set(msg.speaker, {
                    messageCount: 0,
                    totalLength: 0,
                    firstMessage: msg.date,
                    lastMessage: msg.date
                });
            }
            
            const stats = speakerStats.get(msg.speaker);
            stats.messageCount++;
            stats.totalLength += msg.messageLength;
            if (msg.date < stats.firstMessage) stats.firstMessage = msg.date;
            if (msg.date > stats.lastMessage) stats.lastMessage = msg.date;
        });

        const sortedSpeakers = Array.from(speakerStats.entries())
            .sort((a, b) => b[1].messageCount - a[1].messageCount)
            .slice(0, 15); // Top 15 speakers

        return {
            labels: sortedSpeakers.map(([speaker]) => speaker),
            messageCount: sortedSpeakers.map(([, stats]) => stats.messageCount),
            avgLength: sortedSpeakers.map(([, stats]) => Math.round(stats.totalLength / stats.messageCount))
        };
    }

    /**
     * Get timeline pattern data
     */
    getTimelineData() {
        return {
            hourly: {
                labels: Array.from({length: 24}, (_, i) => `${i}:00`),
                data: this.processedData.hourlyStats
            },
            weekly: {
                labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
                data: this.processedData.weeklyStats
            }
        };
    }

    /**
     * Get message distribution data
     */
    getDistributionData() {
        const lengths = this.processedData.messages.map(m => m.messageLength);
        const lengthRanges = [
            { label: '1-10', min: 1, max: 10 },
            { label: '11-50', min: 11, max: 50 },
            { label: '51-100', min: 51, max: 100 },
            { label: '101-200', min: 101, max: 200 },
            { label: '201-500', min: 201, max: 500 },
            { label: '500+', min: 501, max: Infinity }
        ];

        const distribution = lengthRanges.map(range => ({
            label: range.label,
            count: lengths.filter(len => len >= range.min && len <= range.max).length
        }));

        return {
            labels: distribution.map(d => d.label),
            data: distribution.map(d => d.count),
            avgLength: Math.round(lengths.reduce((sum, len) => sum + len, 0) / lengths.length),
            maxLength: Math.max(...lengths),
            minLength: Math.min(...lengths)
        };
    }

    /**
     * Get summary statistics
     */
    getStats() {
        return this.stats;
    }
}
