<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成Excel文件工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #4CAF50;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
        }
        button:hover {
            background: #45a049;
        }
        .info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>生成 发言记录.xlsx 文件</h1>
        
        <div class="info">
            <p><strong>说明：</strong></p>
            <p>点击下面的按钮将生成一个名为 "发言记录.xlsx" 的Excel文件，包含示例聊天记录数据。</p>
            <p>生成的文件可以直接用于测试聊天记录可视化仪表板。</p>
        </div>
        
        <button onclick="generateExcel()">生成 发言记录.xlsx 文件</button>
        
        <div id="status" style="text-align: center; margin-top: 20px;"></div>
    </div>

    <script>
        function generateExcel() {
            const data = [
                ['发言日期', '发言人数', '发言总数', '活跃用户top1', '活跃用户top2', '活跃用户top3', '活跃用户top4', '活跃用户top5'],
                ['2024-01-15', 25, 156, '吴双 (36)', '李明 (28)', '王芳 (22)', '张三 (18)', '赵六 (15)'],
                ['2024-01-16', 30, 189, '李明 (42)', '吴双 (35)', '陈七 (25)', '王芳 (20)', '张三 (16)'],
                ['2024-01-17', 28, 167, '王芳 (38)', '吴双 (32)', '李明 (24)', '陈七 (19)', '刘八 (14)'],
                ['2024-01-18', 32, 201, '陈七 (45)', '李明 (38)', '吴双 (30)', '王芳 (25)', '张三 (20)'],
                ['2024-01-19', 35, 234, '吴双 (48)', '陈七 (41)', '李明 (35)', '王芳 (28)', '赵六 (22)'],
                ['2024-01-20', 22, 134, '李明 (32)', '吴双 (26)', '王芳 (20)', '张三 (16)', '陈七 (12)'],
                ['2024-01-21', 18, 98, '王芳 (24)', '吴双 (18)', '李明 (15)', '陈七 (12)', '张三 (10)'],
                ['2024-01-22', 40, 278, '陈七 (52)', '吴双 (46)', '李明 (40)', '王芳 (32)', '赵六 (28)'],
                ['2024-01-23', 38, 256, '吴双 (50)', '陈七 (44)', '李明 (38)', '王芳 (30)', '张三 (24)'],
                ['2024-01-24', 33, 198, '李明 (41)', '吴双 (36)', '陈七 (28)', '王芳 (24)', '刘八 (18)'],
                ['2024-01-25', 29, 172, '王芳 (35)', '吴双 (30)', '李明 (26)', '陈七 (20)', '张三 (16)'],
                ['2024-01-26', 31, 185, '陈七 (38)', '吴双 (33)', '李明 (28)', '王芳 (22)', '赵六 (18)'],
                ['2024-01-27', 26, 148, '吴双 (32)', '李明 (26)', '王芳 (21)', '陈七 (17)', '张三 (14)'],
                ['2024-01-28', 19, 112, '李明 (28)', '吴双 (22)', '王芳 (18)', '陈七 (14)', '刘八 (11)'],
                ['2024-01-29', 42, 298, '陈七 (58)', '吴双 (52)', '李明 (45)', '王芳 (36)', '赵六 (30)'],
                ['2024-01-30', 39, 267, '吴双 (54)', '陈七 (48)', '李明 (42)', '王芳 (34)', '张三 (26)'],
                ['2024-01-31', 35, 223, '李明 (46)', '吴双 (40)', '陈七 (32)', '王芳 (28)', '刘八 (22)'],
                ['2024-02-01', 28, 165, '王芳 (34)', '吴双 (29)', '李明 (24)', '陈七 (19)', '张三 (15)'],
                ['2024-02-02', 32, 194, '陈七 (40)', '吴双 (35)', '李明 (30)', '王芳 (24)', '赵六 (19)'],
                ['2024-02-03', 27, 151, '吴双 (33)', '李明 (27)', '王芳 (22)', '陈七 (18)', '张三 (14)'],
                ['2024-02-04', 21, 126, '李明 (30)', '吴双 (24)', '王芳 (19)', '陈七 (15)', '刘八 (12)'],
                ['2024-02-05', 44, 312, '陈七 (62)', '吴双 (56)', '李明 (48)', '王芳 (38)', '赵六 (32)'],
                ['2024-02-06', 41, 289, '吴双 (58)', '陈七 (52)', '李明 (44)', '王芳 (36)', '张三 (28)'],
                ['2024-02-07', 36, 234, '李明 (48)', '吴双 (42)', '陈七 (34)', '王芳 (30)', '刘八 (24)'],
                ['2024-02-08', 30, 178, '王芳 (36)', '吴双 (31)', '李明 (26)', '陈七 (21)', '张三 (17)'],
                ['2024-02-09', 34, 206, '陈七 (42)', '吴双 (37)', '李明 (32)', '王芳 (26)', '赵六 (20)'],
                ['2024-02-10', 29, 167, '吴双 (35)', '李明 (29)', '王芳 (24)', '陈七 (19)', '张三 (15)'],
                ['2024-02-11', 23, 138, '李明 (32)', '吴双 (26)', '王芳 (21)', '陈七 (17)', '刘八 (13)'],
                ['2024-02-12', 46, 324, '陈七 (64)', '吴双 (58)', '李明 (50)', '王芳 (40)', '赵六 (34)'],
                ['2024-02-13', 43, 301, '吴双 (60)', '陈七 (54)', '李明 (46)', '王芳 (38)', '张三 (30)']
            ];

            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(data);

            // Set column widths
            ws['!cols'] = [
                { width: 12 }, // 发言日期
                { width: 10 }, // 发言人数
                { width: 10 }, // 发言总数
                { width: 15 }, // 活跃用户top1
                { width: 15 }, // 活跃用户top2
                { width: 15 }, // 活跃用户top3
                { width: 15 }, // 活跃用户top4
                { width: 15 }  // 活跃用户top5
            ];

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, '发言记录');

            // Generate Excel file and download
            XLSX.writeFile(wb, '发言记录.xlsx');

            // Show success message
            document.getElementById('status').innerHTML = 
                '<div style="color: green; font-weight: bold;">✅ 发言记录.xlsx 文件已生成并下载！</div>';
        }
    </script>
</body>
</html>
