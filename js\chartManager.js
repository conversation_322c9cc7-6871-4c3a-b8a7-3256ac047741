/**
 * Chart Manager for Chat Records Visualization
 * Handles creation and management of all chart types
 */

class ChartManager {
    constructor() {
        this.charts = {};
        this.dataProcessor = null;
        this.currentChart = 'trend';
    }

    /**
     * Initialize chart manager with data processor
     * @param {DataProcessor} dataProcessor - Data processor instance
     */
    initialize(dataProcessor) {
        this.dataProcessor = dataProcessor;
    }

    /**
     * Create all charts with processed data
     */
    createAllCharts() {
        if (!this.dataProcessor) {
            console.error('Data processor not initialized');
            return;
        }

        this.createTrendChart();
        this.createSpeakersChart();
        this.createTimelineChart();
        this.createDistributionChart();
    }

    /**
     * Create dual-axis trend chart
     */
    createTrendChart() {
        const data = this.dataProcessor.getChartData('trend');
        if (!data) return;

        const ctx = document.getElementById('trendCanvas').getContext('2d');
        
        // Destroy existing chart if it exists
        if (this.charts.trend) {
            this.charts.trend.destroy();
        }

        this.charts.trend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: 'Message Count',
                        data: data.messageCount,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: 'Unique Speakers',
                        data: data.speakerCount,
                        borderColor: '#f093fb',
                        backgroundColor: 'rgba(240, 147, 251, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    title: {
                        display: true,
                        text: 'Message and Speaker Activity Over Time',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'top',
                        labels: { usePointStyle: true, padding: 20 }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: '#667eea',
                        borderWidth: 1
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'day',
                            displayFormats: {
                                day: 'MMM dd'
                            }
                        },
                        title: {
                            display: true,
                            text: 'Date',
                            font: { weight: 'bold' }
                        },
                        grid: { color: 'rgba(0, 0, 0, 0.1)' }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Message Count',
                            color: '#667eea',
                            font: { weight: 'bold' }
                        },
                        grid: { color: 'rgba(102, 126, 234, 0.1)' },
                        ticks: { color: '#667eea' }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Unique Speakers',
                            color: '#f093fb',
                            font: { weight: 'bold' }
                        },
                        grid: { drawOnChartArea: false },
                        ticks: { color: '#f093fb' }
                    }
                }
            }
        });
    }

    /**
     * Create speaker activity chart
     */
    createSpeakersChart() {
        const data = this.dataProcessor.getChartData('speakers');
        if (!data) return;

        const ctx = document.getElementById('speakersCanvas').getContext('2d');
        
        if (this.charts.speakers) {
            this.charts.speakers.destroy();
        }

        this.charts.speakers = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: 'Message Count',
                        data: data.messageCount,
                        backgroundColor: 'rgba(102, 126, 234, 0.8)',
                        borderColor: '#667eea',
                        borderWidth: 2,
                        borderRadius: 5,
                        borderSkipped: false,
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Top Speakers by Message Count',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        callbacks: {
                            afterLabel: function(context) {
                                const activeDays = data.activeDays[context.dataIndex];
                                const avgRank = data.avgRank[context.dataIndex];
                                const avgPerDay = data.avgMessagesPerDay[context.dataIndex];
                                return [
                                    `Active days: ${activeDays}`,
                                    `Average rank: ${avgRank}`,
                                    `Avg messages/day: ${avgPerDay}`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Speakers',
                            font: { weight: 'bold' }
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        },
                        grid: { display: false }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Message Count',
                            font: { weight: 'bold' }
                        },
                        beginAtZero: true,
                        grid: { color: 'rgba(0, 0, 0, 0.1)' }
                    }
                }
            }
        });
    }

    /**
     * Create timeline patterns chart
     */
    createTimelineChart() {
        const data = this.dataProcessor.getChartData('timeline');
        if (!data) return;

        const ctx = document.getElementById('timelineCanvas').getContext('2d');
        
        if (this.charts.timeline) {
            this.charts.timeline.destroy();
        }

        this.charts.timeline = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.weekly.labels,
                datasets: [
                    {
                        label: 'Messages by Day',
                        data: data.weekly.data,
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                            '#9966FF', '#FF9F40', '#FF6384'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Message Distribution by Day of Week',
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'right',
                        labels: { usePointStyle: true, padding: 15 }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Create message distribution chart
     */
    createDistributionChart() {
        const data = this.dataProcessor.getChartData('distribution');
        if (!data) return;

        const ctx = document.getElementById('distributionCanvas').getContext('2d');
        
        if (this.charts.distribution) {
            this.charts.distribution.destroy();
        }

        this.charts.distribution = new Chart(ctx, {
            type: 'polarArea',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: 'Message Count',
                        data: data.data,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 205, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)'
                        ],
                        borderWidth: 2
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: `Daily Message Count Distribution (Avg: ${data.avgCount} messages/day)`,
                        font: { size: 16, weight: 'bold' }
                    },
                    legend: {
                        position: 'right',
                        labels: { usePointStyle: true, padding: 15 }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        callbacks: {
                            afterLabel: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${percentage}% of total days`;
                            }
                        }
                    }
                },
                scales: {
                    r: {
                        beginAtZero: true,
                        grid: { color: 'rgba(0, 0, 0, 0.1)' },
                        pointLabels: { font: { size: 12 } }
                    }
                }
            }
        });
    }

    /**
     * Switch to a specific chart
     * @param {string} chartType - Chart type to switch to
     */
    switchChart(chartType) {
        console.log('ChartManager.switchChart called with:', chartType);

        // Hide all chart sections
        document.querySelectorAll('.chart-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show selected chart section
        const targetSection = document.getElementById(`${chartType}Chart`);
        console.log('Target section:', targetSection);

        if (targetSection) {
            targetSection.classList.add('active');
            this.currentChart = chartType;
            console.log('Switched to chart:', chartType);

            // Trigger chart resize to ensure proper display
            if (this.charts[chartType]) {
                setTimeout(() => {
                    this.charts[chartType].resize();
                }, 100);
            }
        } else {
            console.error('Chart section not found:', `${chartType}Chart`);
        }
    }

    /**
     * Destroy all charts
     */
    destroyAllCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart) {
                chart.destroy();
            }
        });
        this.charts = {};
    }

    /**
     * Update charts with new data
     */
    updateCharts() {
        this.destroyAllCharts();
        this.createAllCharts();
    }
}
