# Chat Records Visualization Dashboard

A comprehensive interactive dashboard for visualizing chat record data from Excel files. This application provides multiple chart types with easy switching functionality to analyze message patterns, speaker activity, and communication trends.

## Features

### 📊 Multiple Chart Types
- **Dual-Axis Trend Chart**: Shows message count and unique speaker count over time
- **Speaker Activity Chart**: Displays top speakers by message count with average message length
- **Timeline Patterns Chart**: Visualizes message distribution by day of the week
- **Message Distribution Chart**: Analyzes message length patterns and distribution

### 🎯 Key Capabilities
- **Excel File Support**: Reads .xlsx and .xls files directly in the browser
- **Intelligent Data Parsing**: Automatically detects column types (timestamp, speaker, message content)
- **Responsive Design**: Works seamlessly across desktop, tablet, and mobile devices
- **Interactive Charts**: Hover tooltips, legends, and detailed information
- **Real-time Statistics**: Summary statistics updated automatically
- **Professional Styling**: Modern, clean interface with smooth animations

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Excel file containing chat records

### Installation
1. Download all files to a local directory
2. Open `index.html` in your web browser
3. No additional installation or server setup required!

### Auto-Loading Default File
The application will automatically try to load a file named `发言记录.xlsx` from the same directory when the page loads. If this file exists, the charts will be generated automatically without manual upload.

### Sample Data
Multiple sample data options are provided:
1. **CSV Sample**: `sample_data.csv` - Can be opened in Excel and saved as .xlsx
2. **Excel Generator**: Open `generate_excel.html` to create a sample `发言记录.xlsx` file
3. **Manual Creation**: Create your own Excel file following the format below

### File Structure
```
├── index.html          # Main HTML page
├── styles.css          # CSS styling and responsive design
├── dataProcessor.js    # Excel file reading and data processing
├── chartManager.js     # Chart creation and management
├── app.js             # Main application controller
└── README.md          # This documentation
```

## Usage

### Method 1: Auto-Load (Recommended)
1. Place your Excel file named `发言记录.xlsx` in the same directory as `index.html`
2. Open `index.html` in your browser
3. The application will automatically load and process the file
4. Charts will be generated automatically

### Method 2: Manual Upload
1. Open `index.html` in your browser
2. Click the "📁 Upload Excel File" button
3. Select your chat records Excel file (.xlsx or .xls)
4. The application will process and analyze the data

### Navigate Charts
Use the navigation buttons to switch between different visualizations:
- **📈 Daily Activity Trend**: View daily message and speaker count trends over time
- **👥 Top Active Users**: See the most active participants with detailed statistics
- **⏰ Weekly Activity Patterns**: Understand weekly communication patterns
- **📊 Daily Message Count Distribution**: Analyze daily message volume patterns

### Interpret Results
- **Statistics Summary**: View key metrics at the bottom of the page
- **Interactive Elements**: Hover over chart elements for detailed information
- **Responsive Design**: Charts automatically adjust to your screen size
- **Zero-Based Axes**: Trend chart axes always start from zero for accurate comparison

## Excel File Format

### Required Columns
The application expects the following specific column names:

#### Required Columns
- **发言日期**: Date of the chat activity
- **发言人数**: Number of unique speakers for that day
- **发言总数**: Total number of messages for that day
- **活跃用户top1**: Most active user with format "用户名 (消息数量)"
- **活跃用户top2**: Second most active user with format "用户名 (消息数量)"
- **活跃用户top3**: Third most active user with format "用户名 (消息数量)"
- **活跃用户top4**: Fourth most active user with format "用户名 (消息数量)"
- **活跃用户top5**: Fifth most active user with format "用户名 (消息数量)"

### Example Excel Structure
```
| 发言日期    | 发言人数 | 发言总数 | 活跃用户top1  | 活跃用户top2  | 活跃用户top3  | 活跃用户top4  | 活跃用户top5  |
|------------|---------|---------|-------------|-------------|-------------|-------------|-------------|
| 2024-01-15 | 25      | 156     | 吴双 (36)    | 李明 (28)    | 王芳 (22)    | 张三 (18)    | 赵六 (15)    |
| 2024-01-16 | 30      | 189     | 李明 (42)    | 吴双 (35)    | 陈七 (25)    | 王芳 (20)    | 张三 (16)    |
```

### Data Format Requirements
- **Date Format**: Any standard date format (YYYY-MM-DD, MM/DD/YYYY, etc.)
- **User Format**: Must follow pattern "用户名 (数量)" where 数量 is the message count
- **Numbers**: Speaker count and message count should be integers
- **Empty Cells**: Top user cells can be empty if fewer than 5 active users for that day

## Technical Details

### Libraries Used
- **Chart.js**: Modern charting library for interactive visualizations
- **SheetJS**: Excel file reading and parsing
- **Date-fns**: Date handling and formatting for Chart.js

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Performance
- Handles files with thousands of messages efficiently
- Client-side processing (no server required)
- Optimized chart rendering and data processing

## Chart Types Explained

### 1. Daily Activity Trend Chart
- **Purpose**: Shows correlation between daily message volume and speaker participation
- **Y-Axis Left**: Total message count per day
- **Y-Axis Right**: Number of unique speakers per day
- **Use Case**: Identify peak activity periods and engagement patterns over time

### 2. Top Active Users Chart
- **Purpose**: Identifies most active participants across all days
- **Display**: Horizontal bar chart of top 15 users by total message count
- **Additional Info**: Shows active days, average rank, and average messages per day in tooltips
- **Use Case**: Understand participation distribution and identify key contributors

### 3. Weekly Activity Patterns Chart
- **Purpose**: Reveals weekly communication patterns
- **Display**: Doughnut chart showing message distribution by day of week
- **Use Case**: Identify which days of the week have the most chat activity

### 4. Daily Message Count Distribution Chart
- **Purpose**: Analyzes daily message volume patterns
- **Display**: Polar area chart showing distribution of daily message counts
- **Metrics**: Shows how many days fall into different message count ranges
- **Use Case**: Understand typical daily activity levels and identify outlier days

## Troubleshooting

### Common Issues

**File Upload Fails**
- Ensure file is in .xlsx or .xls format
- Check that file is not corrupted
- Verify file size is reasonable (< 50MB recommended)

**Charts Not Displaying**
- Check browser console for error messages
- Ensure Excel file has recognizable column headers
- Verify date/time data is in a parseable format

**Data Not Recognized**
- Check column headers match expected patterns
- Ensure data rows contain actual content
- Verify timestamp format is consistent

### Getting Help
If you encounter issues:
1. Check the browser console for error messages
2. Verify your Excel file format matches the expected structure
3. Try with a smaller sample file first

## Future Enhancements

Potential improvements for future versions:
- Export charts as images
- Additional chart types (heatmaps, network graphs)
- Data filtering and date range selection
- Sentiment analysis integration
- Multi-file comparison capabilities

## License

This project is open source and available under the MIT License.
