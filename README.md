# Chat Records Visualization Dashboard

A comprehensive interactive dashboard for visualizing chat record data from Excel files. This application provides multiple chart types with easy switching functionality to analyze message patterns, speaker activity, and communication trends.

## Features

### 📊 Multiple Chart Types
- **Dual-Axis Trend Chart**: Shows message count and unique speaker count over time
- **Speaker Activity Chart**: Displays top speakers by message count with average message length
- **Timeline Patterns Chart**: Visualizes message distribution by day of the week
- **Message Distribution Chart**: Analyzes message length patterns and distribution

### 🎯 Key Capabilities
- **Excel File Support**: Reads .xlsx and .xls files directly in the browser
- **Intelligent Data Parsing**: Automatically detects column types (timestamp, speaker, message content)
- **Responsive Design**: Works seamlessly across desktop, tablet, and mobile devices
- **Interactive Charts**: Hover tooltips, legends, and detailed information
- **Real-time Statistics**: Summary statistics updated automatically
- **Professional Styling**: Modern, clean interface with smooth animations

## Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Excel file containing chat records

### Installation
1. Download all files to a local directory
2. Open `index.html` in your web browser
3. No additional installation or server setup required!

### File Structure
```
├── index.html          # Main HTML page
├── styles.css          # CSS styling and responsive design
├── dataProcessor.js    # Excel file reading and data processing
├── chartManager.js     # Chart creation and management
├── app.js             # Main application controller
└── README.md          # This documentation
```

## Usage

### 1. Upload Excel File
- Click the "📁 Upload Excel File" button
- Select your chat records Excel file (.xlsx or .xls)
- The application will automatically process and analyze the data

### 2. Navigate Charts
Use the navigation buttons to switch between different visualizations:
- **📈 Trend Analysis**: View message and speaker trends over time
- **👥 Speaker Activity**: See who are the most active participants
- **⏰ Timeline Patterns**: Understand when conversations happen most
- **📊 Message Distribution**: Analyze message length patterns

### 3. Interpret Results
- **Statistics Summary**: View key metrics at the bottom of the page
- **Interactive Elements**: Hover over chart elements for detailed information
- **Responsive Design**: Charts automatically adjust to your screen size

## Excel File Format

### Required Columns
The application automatically detects columns based on common naming patterns:

#### Timestamp/Date Columns
- 时间, timestamp, datetime, 发言时间
- 日期, date
- 时刻, time

#### Speaker Columns  
- 发言人, speaker, 用户, user, 姓名, name

#### Message Content Columns
- 内容, message, 发言内容, text, 消息, content

### Example Excel Structure
```
| 发言时间          | 发言人    | 发言内容        |
|------------------|----------|----------------|
| 2024-01-15 09:30 | Alice    | Hello everyone |
| 2024-01-15 09:31 | Bob      | Good morning   |
| 2024-01-15 09:32 | Charlie  | How are you?   |
```

### Supported Date Formats
- ISO format: `2024-01-15 09:30:00`
- Excel serial dates
- Various regional formats: `MM/DD/YYYY HH:MM`, `DD/MM/YYYY HH:MM`
- Separate date and time columns

## Technical Details

### Libraries Used
- **Chart.js**: Modern charting library for interactive visualizations
- **SheetJS**: Excel file reading and parsing
- **Date-fns**: Date handling and formatting for Chart.js

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Performance
- Handles files with thousands of messages efficiently
- Client-side processing (no server required)
- Optimized chart rendering and data processing

## Chart Types Explained

### 1. Dual-Axis Trend Chart
- **Purpose**: Shows correlation between message volume and speaker participation
- **Y-Axis Left**: Message count per day
- **Y-Axis Right**: Number of unique speakers per day
- **Use Case**: Identify peak activity periods and engagement patterns

### 2. Speaker Activity Chart
- **Purpose**: Identifies most active participants
- **Display**: Horizontal bar chart of top 15 speakers
- **Additional Info**: Average message length shown in tooltips
- **Use Case**: Understand participation distribution and identify key contributors

### 3. Timeline Patterns Chart
- **Purpose**: Reveals when conversations typically occur
- **Display**: Doughnut chart showing distribution by day of week
- **Use Case**: Identify patterns in communication timing

### 4. Message Distribution Chart
- **Purpose**: Analyzes message length characteristics
- **Display**: Polar area chart showing length ranges
- **Metrics**: Average, minimum, and maximum message lengths
- **Use Case**: Understand communication style and message complexity

## Troubleshooting

### Common Issues

**File Upload Fails**
- Ensure file is in .xlsx or .xls format
- Check that file is not corrupted
- Verify file size is reasonable (< 50MB recommended)

**Charts Not Displaying**
- Check browser console for error messages
- Ensure Excel file has recognizable column headers
- Verify date/time data is in a parseable format

**Data Not Recognized**
- Check column headers match expected patterns
- Ensure data rows contain actual content
- Verify timestamp format is consistent

### Getting Help
If you encounter issues:
1. Check the browser console for error messages
2. Verify your Excel file format matches the expected structure
3. Try with a smaller sample file first

## Future Enhancements

Potential improvements for future versions:
- Export charts as images
- Additional chart types (heatmaps, network graphs)
- Data filtering and date range selection
- Sentiment analysis integration
- Multi-file comparison capabilities

## License

This project is open source and available under the MIT License.
