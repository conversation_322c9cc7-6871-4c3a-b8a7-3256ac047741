# 聊天记录可视化仪表板 - 使用说明

## 🚀 快速开始

### 方法1：查看示例数据（最简单）
1. 直接打开 `index.html` 文件
2. 页面会自动加载示例数据并显示图表
3. 使用导航按钮切换不同的图表视图

### 方法2：使用您自己的数据（推荐）
1. 打开 `data_converter.html`（数据转换工具）
2. 上传您的 `发言记录.xlsx` 文件
3. 点击"下载 default_data.js 文件"
4. 将下载的文件替换项目中的 `default_data.js`
5. 重新打开 `index.html`，即可看到您的数据

### 方法3：手动上传
1. 打开 `index.html`
2. 点击"📁 Upload Excel File"按钮
3. 选择您的Excel文件上传

## 📊 Excel文件格式要求

您的Excel文件必须包含以下列：

| 列名 | 说明 | 示例 |
|------|------|------|
| 发言日期 | 聊天活动的日期 | 2024-01-15 |
| 发言人数 | 当天的唯一发言人数量 | 25 |
| 发言总数 | 当天的消息总数 | 156 |
| 活跃用户top1 | 最活跃用户及消息数 | 吴双 (36) |
| 活跃用户top2 | 第二活跃用户及消息数 | 李明 (28) |
| 活跃用户top3 | 第三活跃用户及消息数 | 王芳 (22) |
| 活跃用户top4 | 第四活跃用户及消息数 | 张三 (18) |
| 活跃用户top5 | 第五活跃用户及消息数 | 赵六 (15) |

### 重要格式说明：
- **日期格式**：支持多种格式（YYYY-MM-DD, MM/DD/YYYY等）
- **用户格式**：必须是 "用户名 (数量)" 的格式，如 "张三 (25)"
- **数字格式**：发言人数和发言总数必须是整数
- **空值处理**：如果某天活跃用户不足5人，对应的top用户列可以为空

## 🎨 图表说明

### 1. 📈 Daily Activity Trend Analysis（每日活跃趋势分析）
- **功能**：显示每日消息数量和发言人数的双轴趋势图
- **Y轴左侧**：每日消息总数
- **Y轴右侧**：每日发言人数
- **用途**：识别活跃高峰期和参与模式

### 2. 👥 Top Active Users（顶级活跃用户）
- **功能**：显示总消息数最多的前15名用户
- **显示信息**：总消息数、活跃天数、平均排名、日均消息数
- **用途**：识别关键贡献者和参与分布

### 3. ⏰ Weekly Activity Patterns（周活跃模式）
- **功能**：按星期几显示消息分布
- **图表类型**：圆环图
- **用途**：了解一周中的沟通模式

### 4. 📊 Daily Message Count Distribution（每日消息数分布）
- **功能**：显示不同消息数量范围的天数分布
- **图表类型**：极坐标图
- **用途**：分析典型的日活跃水平

## 🛠 工具文件说明

### `data_converter.html` - 数据转换工具
- **功能**：将Excel文件转换为JavaScript数据文件
- **优势**：转换后的数据可以自动加载，无需每次手动上传
- **使用**：拖拽或选择Excel文件，自动生成 `default_data.js`

### `generate_excel.html` - Excel生成器
- **功能**：生成示例Excel文件用于测试
- **用途**：了解正确的数据格式，创建测试数据

### `debug.html` - 调试页面
- **功能**：测试基本的图表切换功能
- **用途**：排查问题，验证功能

## 🔧 故障排除

### 问题1：页面打开后没有显示图表
**解决方案**：
1. 检查浏览器控制台是否有错误信息
2. 确认 `default_data.js` 文件存在且格式正确
3. 尝试手动上传Excel文件

### 问题2：图表切换按钮无反应
**解决方案**：
1. 刷新页面重试
2. 检查浏览器控制台的错误信息
3. 确认数据已正确加载

### 问题3：Excel文件上传失败
**解决方案**：
1. 检查文件格式是否为 .xlsx 或 .xls
2. 确认文件包含所有必需的列
3. 检查数据格式是否正确（特别是用户名格式）

### 问题4：数据转换工具无法使用
**解决方案**：
1. 确认使用现代浏览器（Chrome、Firefox、Safari、Edge）
2. 检查Excel文件是否包含正确的列名
3. 确认用户数据格式为 "用户名 (数量)"

## 📞 技术支持

如果遇到问题：
1. 查看浏览器控制台的错误信息
2. 检查 `CHANGELOG.md` 了解已知问题
3. 使用 `debug.html` 测试基本功能
4. 确认文件格式符合要求

## 🎯 最佳实践

1. **推荐使用数据转换工具**：一次转换，多次使用
2. **定期备份数据**：保存原始Excel文件和转换后的JS文件
3. **使用现代浏览器**：确保最佳兼容性和性能
4. **检查数据质量**：确保Excel数据完整且格式正确

---

**版本**：2.3  
**更新时间**：2024年1月  
**兼容性**：Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
