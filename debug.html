<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Chat Visualization</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Debug Chat Records Visualization</h1>
            <div class="file-upload-section">
                <label for="excelFile" class="upload-btn">
                    <span>📁 Upload Excel File</span>
                    <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;">
                </label>
                <span id="fileName" class="file-name"></span>
            </div>
        </header>

        <nav class="chart-navigation">
            <button class="nav-btn active" data-chart="trend" onclick="switchChart('trend')">📈 Trend Analysis</button>
            <button class="nav-btn" data-chart="speakers" onclick="switchChart('speakers')">👥 Speaker Activity</button>
            <button class="nav-btn" data-chart="timeline" onclick="switchChart('timeline')">⏰ Timeline Patterns</button>
            <button class="nav-btn" data-chart="distribution" onclick="switchChart('distribution')">📊 Message Distribution</button>
        </nav>

        <main class="chart-container">
            <div id="loadingIndicator" class="loading">
                <div class="spinner"></div>
                <p>Processing data...</p>
            </div>

            <div id="noDataMessage" class="no-data show">
                <h3>📋 Debug Mode</h3>
                <p>Click the buttons above to test chart switching.</p>
                <p>Upload an Excel file to test data processing.</p>
            </div>

            <!-- Trend Chart (Dual-axis) -->
            <div id="trendChart" class="chart-section active">
                <div class="chart-header">
                    <h2>📈 Daily Activity Trend Analysis</h2>
                    <p>Dual-axis chart showing daily message count and speaker count over time</p>
                </div>
                <div class="chart-wrapper">
                    <p>Trend chart would appear here</p>
                </div>
            </div>

            <!-- Speaker Activity Chart -->
            <div id="speakersChart" class="chart-section">
                <div class="chart-header">
                    <h2>👥 Top Active Users</h2>
                    <p>Most active users by total message count with activity statistics</p>
                </div>
                <div class="chart-wrapper">
                    <p>Speakers chart would appear here</p>
                </div>
            </div>

            <!-- Timeline Patterns Chart -->
            <div id="timelineChart" class="chart-section">
                <div class="chart-header">
                    <h2>⏰ Weekly Activity Patterns</h2>
                    <p>Message activity distribution by day of the week</p>
                </div>
                <div class="chart-wrapper">
                    <p>Timeline chart would appear here</p>
                </div>
            </div>

            <!-- Message Distribution Chart -->
            <div id="distributionChart" class="chart-section">
                <div class="chart-header">
                    <h2>📊 Daily Message Count Distribution</h2>
                    <p>Distribution of daily message counts across different ranges</p>
                </div>
                <div class="chart-wrapper">
                    <p>Distribution chart would appear here</p>
                </div>
            </div>
        </main>
    </div>

    <script>
        console.log('Debug page loaded');
        
        function switchChart(chartType) {
            console.log('switchChart called with:', chartType);
            
            // Update active button
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            const activeBtn = document.querySelector(`[data-chart="${chartType}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }
            
            // Hide all chart sections
            document.querySelectorAll('.chart-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // Show selected chart section
            const targetSection = document.getElementById(`${chartType}Chart`);
            console.log('Target section:', targetSection);
            
            if (targetSection) {
                targetSection.classList.add('active');
                console.log('Successfully switched to:', chartType);
            } else {
                console.error('Chart section not found:', `${chartType}Chart`);
            }
        }
        
        // Test file upload
        document.getElementById('excelFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                console.log('File selected:', file.name);
                document.getElementById('fileName').textContent = file.name;
                
                // Hide no data message
                document.getElementById('noDataMessage').classList.remove('show');
            }
        });
        
        console.log('Debug page setup complete');
    </script>
</body>
</html>
